# frozen_string_literal: true

# Background job for scheduled synchronization of subscription plans from Stripe
# Runs daily to catch any missed webhook events and ensure data consistency
class SubscriptionPlanSyncJob < ApplicationJob
  queue_as :default

  # Retry configuration for failed sync attempts
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(sync_type: 'full', force_sync: false, price_ids: [])
    Rails.logger.info "SubscriptionPlanSyncJob: Starting #{sync_type} sync (force: #{force_sync})"
    
    case sync_type.to_s
    when 'full'
      perform_full_sync(force_sync)
    when 'bulk'
      perform_bulk_sync(price_ids, force_sync)
    when 'health_check'
      perform_health_check_sync
    else
      Rails.logger.error "SubscriptionPlanSyncJob: Unknown sync type '#{sync_type}'"
      raise ArgumentError, "Unknown sync type: #{sync_type}"
    end
  end

  private

  # Perform full synchronization of all plans from Stripe
  def perform_full_sync(force_sync)
    start_time = Time.current
    
    begin
      sync_service = SubscriptionPlanSyncService.new(
        logger: Rails.logger,
        sync_batch_id: "scheduled_full_#{start_time.to_i}"
      )
      
      result = sync_service.sync_all_from_stripe
      
      if result[:success]
        Rails.logger.info "SubscriptionPlanSyncJob: Full sync completed successfully. " \
                         "Created: #{result[:created_count]}, Updated: #{result[:updated_count]}, " \
                         "Total synced: #{result[:synced_count]}"
        
        # Update sync metrics
        update_sync_metrics('full', result, start_time)
        
        # Send notification if significant changes detected
        notify_if_significant_changes(result)
      else
        Rails.logger.error "SubscriptionPlanSyncJob: Full sync failed: #{result[:errors]&.join(', ')}"
        raise StandardError, "Full sync failed: #{result[:errors]&.first}"
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlanSyncJob: Full sync error: #{e.message}"
      update_sync_metrics('full', { success: false, error: e.message }, start_time)
      raise
    end
  end

  # Perform bulk synchronization of specific plans
  def perform_bulk_sync(price_ids, force_sync)
    start_time = Time.current
    
    if price_ids.empty?
      Rails.logger.warn "SubscriptionPlanSyncJob: No price IDs provided for bulk sync"
      return
    end
    
    begin
      sync_service = SubscriptionPlanSyncService.new(
        logger: Rails.logger,
        sync_batch_id: "scheduled_bulk_#{start_time.to_i}"
      )
      
      result = sync_service.bulk_sync_from_stripe(price_ids: price_ids, force_sync: force_sync)
      
      if result[:success]
        Rails.logger.info "SubscriptionPlanSyncJob: Bulk sync completed successfully. " \
                         "Created: #{result[:created_count]}, Updated: #{result[:updated_count]}, " \
                         "Skipped: #{result[:skipped_count]}, Total synced: #{result[:synced_count]}"
        
        update_sync_metrics('bulk', result, start_time)
      else
        Rails.logger.error "SubscriptionPlanSyncJob: Bulk sync failed: #{result[:errors]&.join(', ')}"
        raise StandardError, "Bulk sync failed: #{result[:errors]&.first}"
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlanSyncJob: Bulk sync error: #{e.message}"
      update_sync_metrics('bulk', { success: false, error: e.message }, start_time)
      raise
    end
  end

  # Perform health check sync to verify data consistency
  def perform_health_check_sync
    start_time = Time.current
    
    begin
      Rails.logger.info "SubscriptionPlanSyncJob: Starting health check sync"
      
      # Get all local plans with Stripe price IDs
      local_plans = SubscriptionPlan.where.not(stripe_price_id: nil)
      inconsistencies = []
      
      local_plans.find_each do |plan|
        next unless plan.last_synced_at.nil? || plan.last_synced_at < 24.hours.ago
        
        # Check if plan needs sync (hasn't been synced in 24 hours)
        inconsistencies << plan.stripe_price_id
      end
      
      if inconsistencies.any?
        Rails.logger.info "SubscriptionPlanSyncJob: Found #{inconsistencies.count} plans needing sync"
        perform_bulk_sync(inconsistencies, false)
      else
        Rails.logger.info "SubscriptionPlanSyncJob: Health check passed - all plans are up to date"
        update_sync_metrics('health_check', { success: true, checked_count: local_plans.count }, start_time)
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlanSyncJob: Health check sync error: #{e.message}"
      update_sync_metrics('health_check', { success: false, error: e.message }, start_time)
      raise
    end
  end

  # Update sync metrics in cache for monitoring
  def update_sync_metrics(sync_type, result, start_time)
    duration = Time.current - start_time
    
    metrics = {
      sync_type: sync_type,
      timestamp: Time.current,
      duration_seconds: duration.round(2),
      success: result[:success],
      created_count: result[:created_count] || 0,
      updated_count: result[:updated_count] || 0,
      skipped_count: result[:skipped_count] || 0,
      synced_count: result[:synced_count] || 0,
      checked_count: result[:checked_count] || 0,
      error: result[:error],
      errors: result[:errors]
    }
    
    # Store last sync metrics
    Rails.cache.write("subscription_plan_sync_last_#{sync_type}", metrics, expires_in: 7.days)
    
    # Store sync history (keep last 10 syncs)
    history_key = "subscription_plan_sync_history_#{sync_type}"
    history = Rails.cache.read(history_key) || []
    history.unshift(metrics)
    history = history.first(10) # Keep only last 10 entries
    Rails.cache.write(history_key, history, expires_in: 7.days)
    
    Rails.logger.info "SubscriptionPlanSyncJob: Updated sync metrics for #{sync_type} (duration: #{duration.round(2)}s)"
  end

  # Send notification if significant changes are detected
  def notify_if_significant_changes(result)
    total_changes = (result[:created_count] || 0) + (result[:updated_count] || 0)
    
    # Notify if more than 5 plans were changed (could indicate major Stripe changes)
    if total_changes > 5
      Rails.logger.warn "SubscriptionPlanSyncJob: Significant changes detected - #{total_changes} plans modified"
      
      # Store alert for admin dashboard
      Rails.cache.write(
        'subscription_plan_sync_alert',
        {
          message: "Significant subscription plan changes detected: #{total_changes} plans modified",
          timestamp: Time.current,
          details: result
        },
        expires_in: 24.hours
      )
    end
  end
end
