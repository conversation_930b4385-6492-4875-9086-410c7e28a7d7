# frozen_string_literal: true

# Service for synchronizing subscription plans between local database and Stripe API
# Handles creating, updating, and syncing plan data with proper error handling
# Enhanced with bulk operations, status tracking, and webhook integration
class SubscriptionPlanSyncService
  include ActiveModel::Model
  include ActiveModel::Attributes

  # Custom error classes
  class SyncError < StandardError; end
  class StripeConnectionError < SyncError; end
  class ValidationError < SyncError; end
  class BulkSyncError < SyncError; end

  # Sync status constants
  SYNC_STATUS_PENDING = 'pending'.freeze
  SYNC_STATUS_IN_PROGRESS = 'in_progress'.freeze
  SYNC_STATUS_COMPLETED = 'completed'.freeze
  SYNC_STATUS_FAILED = 'failed'.freeze

  attr_accessor :logger, :sync_batch_id

  def initialize(logger: Rails.logger, sync_batch_id: nil)
    @logger = logger
    @sync_batch_id = sync_batch_id || SecureRandom.uuid
  end

  # Class method wrappers for controller convenience
  def self.sync_plan_from_stripe(stripe_price_id)
    new.sync_plan_from_stripe(stripe_price_id)
  end

  def self.sync_all_from_stripe
    new.sync_all_from_stripe
  end

  # Enhanced class methods for bulk operations
  def self.bulk_sync_from_stripe(price_ids: [], force_sync: false)
    new.bulk_sync_from_stripe(price_ids: price_ids, force_sync: force_sync)
  end

  def self.sync_from_webhook(webhook_data)
    new.sync_from_webhook(webhook_data)
  end

  def self.get_sync_status(sync_batch_id)
    new.get_sync_status(sync_batch_id)
  end

  def self.force_sync_features_for_plan(stripe_price_id)
    new.force_sync_features_for_plan(stripe_price_id)
  end

  def self.debug_plan_sync(stripe_price_id)
    new.debug_plan_sync(stripe_price_id)
  end

  # Sync all plans from Stripe to local database
  def sync_all_from_stripe
    logger.info "SubscriptionPlanSyncService: Starting sync of all plans from Stripe (batch: #{sync_batch_id})"

    update_sync_status(SYNC_STATUS_IN_PROGRESS, { message: 'Starting full sync from Stripe' })

    unless stripe_configured?
      error_msg = 'Stripe is not properly configured'
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      return { success: false, errors: [error_msg] }
    end

    begin
      stripe_prices = fetch_all_stripe_prices
      sync_results = { created: 0, updated: 0, errors: [], total: stripe_prices.count }

      update_sync_status(SYNC_STATUS_IN_PROGRESS, {
        message: "Processing #{stripe_prices.count} plans from Stripe",
        progress: { current: 0, total: stripe_prices.count }
      })

      stripe_prices.each_with_index do |stripe_price, index|
        begin
          result = sync_plan_from_stripe_data(stripe_price)
          sync_results[result] += 1 if [:created, :updated].include?(result)

          # Update progress every 10 plans
          if (index + 1) % 10 == 0 || index == stripe_prices.count - 1
            update_sync_status(SYNC_STATUS_IN_PROGRESS, {
              message: "Processed #{index + 1}/#{stripe_prices.count} plans",
              progress: { current: index + 1, total: stripe_prices.count }
            })
          end
        rescue StandardError => e
          error_msg = "Failed to sync plan #{stripe_price.id}: #{e.message}"
          logger.error "SubscriptionPlanSyncService: #{error_msg}"
          sync_results[:errors] << error_msg
        end
      end

      total_synced = sync_results[:created] + sync_results[:updated]
      success = sync_results[:errors].empty? || total_synced > 0

      final_status = success ? SYNC_STATUS_COMPLETED : SYNC_STATUS_FAILED
      update_sync_status(final_status, {
        message: "Sync completed. Created: #{sync_results[:created]}, Updated: #{sync_results[:updated]}, Errors: #{sync_results[:errors].count}",
        results: sync_results
      })

      logger.info "SubscriptionPlanSyncService: Sync completed (batch: #{sync_batch_id}). Created: #{sync_results[:created]}, Updated: #{sync_results[:updated]}, Errors: #{sync_results[:errors].count}"

      {
        success: success,
        sync_batch_id: sync_batch_id,
        synced_count: total_synced,
        created_count: sync_results[:created],
        updated_count: sync_results[:updated],
        errors: sync_results[:errors]
      }
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error during sync: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      { success: false, sync_batch_id: sync_batch_id, errors: [error_msg] }
    rescue StandardError => e
      error_msg = "Unexpected error during sync: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      { success: false, sync_batch_id: sync_batch_id, errors: [error_msg] }
    end
  end

  # Sync individual plan from Stripe by price ID
  def sync_plan_from_stripe(stripe_price_id)
    logger.info "SubscriptionPlanSyncService: Syncing plan #{stripe_price_id} from Stripe (batch: #{sync_batch_id})"

    unless stripe_configured?
      return { success: false, errors: ['Stripe is not properly configured'] }
    end

    begin
      stripe_price = Stripe::Price.retrieve(stripe_price_id)
      # Fetch the product separately if needed
      if stripe_price.product.is_a?(String)
        stripe_price.product = Stripe::Product.retrieve(stripe_price.product)
      end

      result = sync_plan_from_stripe_data(stripe_price)

      if result == :created || result == :updated
        { success: true, action: result, stripe_price_id: stripe_price_id, sync_batch_id: sync_batch_id }
      else
        { success: false, errors: ["Failed to sync plan #{stripe_price_id}"], sync_batch_id: sync_batch_id }
      end
    rescue Stripe::InvalidRequestError => e
      error_msg = "Invalid Stripe price ID #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, errors: [error_msg], sync_batch_id: sync_batch_id }
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error for #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, errors: [error_msg], sync_batch_id: sync_batch_id }
    rescue StandardError => e
      error_msg = "Unexpected error syncing #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, errors: [error_msg], sync_batch_id: sync_batch_id }
    end
  end

  # Bulk sync specific plans from Stripe
  def bulk_sync_from_stripe(price_ids: [], force_sync: false)
    logger.info "SubscriptionPlanSyncService: Starting bulk sync for #{price_ids.count} plans (batch: #{sync_batch_id})"

    update_sync_status(SYNC_STATUS_IN_PROGRESS, {
      message: "Starting bulk sync for #{price_ids.count} plans",
      price_ids: price_ids,
      force_sync: force_sync
    })

    unless stripe_configured?
      error_msg = 'Stripe is not properly configured'
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      return { success: false, errors: [error_msg] }
    end

    if price_ids.empty?
      error_msg = 'No price IDs provided for bulk sync'
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      return { success: false, errors: [error_msg] }
    end

    begin
      sync_results = { created: 0, updated: 0, skipped: 0, errors: [], total: price_ids.count }

      price_ids.each_with_index do |price_id, index|
        begin
          # Skip if already synced recently unless force_sync is true
          if !force_sync && recently_synced?(price_id)
            sync_results[:skipped] += 1
            logger.info "SubscriptionPlanSyncService: Skipping recently synced plan #{price_id}"
            next
          end

          stripe_price = Stripe::Price.retrieve(price_id)
          result = sync_plan_from_stripe_data(stripe_price)
          sync_results[result] += 1 if [:created, :updated].include?(result)

          # Update progress
          update_sync_status(SYNC_STATUS_IN_PROGRESS, {
            message: "Processed #{index + 1}/#{price_ids.count} plans",
            progress: { current: index + 1, total: price_ids.count }
          })
        rescue StandardError => e
          error_msg = "Failed to sync plan #{price_id}: #{e.message}"
          logger.error "SubscriptionPlanSyncService: #{error_msg}"
          sync_results[:errors] << error_msg
        end
      end

      total_synced = sync_results[:created] + sync_results[:updated]
      success = sync_results[:errors].empty? || total_synced > 0

      final_status = success ? SYNC_STATUS_COMPLETED : SYNC_STATUS_FAILED
      update_sync_status(final_status, {
        message: "Bulk sync completed. Created: #{sync_results[:created]}, Updated: #{sync_results[:updated]}, Skipped: #{sync_results[:skipped]}, Errors: #{sync_results[:errors].count}",
        results: sync_results
      })

      logger.info "SubscriptionPlanSyncService: Bulk sync completed (batch: #{sync_batch_id}). Created: #{sync_results[:created]}, Updated: #{sync_results[:updated]}, Skipped: #{sync_results[:skipped]}, Errors: #{sync_results[:errors].count}"

      {
        success: success,
        sync_batch_id: sync_batch_id,
        synced_count: total_synced,
        created_count: sync_results[:created],
        updated_count: sync_results[:updated],
        skipped_count: sync_results[:skipped],
        errors: sync_results[:errors]
      }
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error during bulk sync: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      { success: false, sync_batch_id: sync_batch_id, errors: [error_msg] }
    rescue StandardError => e
      error_msg = "Unexpected error during bulk sync: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      update_sync_status(SYNC_STATUS_FAILED, { error: error_msg })
      { success: false, sync_batch_id: sync_batch_id, errors: [error_msg] }
    end
  end

  # Sync from webhook data
  def sync_from_webhook(webhook_data)
    logger.info "SubscriptionPlanSyncService: Processing webhook sync (batch: #{sync_batch_id})"

    unless webhook_data.is_a?(Hash) && webhook_data['type'].present?
      error_msg = 'Invalid webhook data format'
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      return { success: false, errors: [error_msg] }
    end

    begin
      case webhook_data['type']
      when 'price.created', 'price.updated'
        handle_price_webhook(webhook_data['data']['object'])
      when 'product.updated'
        handle_product_webhook(webhook_data['data']['object'])
      else
        logger.info "SubscriptionPlanSyncService: Ignoring webhook type #{webhook_data['type']}"
        return { success: true, action: :ignored, webhook_type: webhook_data['type'], sync_batch_id: sync_batch_id }
      end
    rescue StandardError => e
      error_msg = "Failed to process webhook: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, errors: [error_msg], sync_batch_id: sync_batch_id }
    end
  end

  # Get sync status for a batch
  def get_sync_status(batch_id = nil)
    target_batch_id = batch_id || sync_batch_id
    Rails.cache.read("subscription_plan_sync_status:#{target_batch_id}") || {
      status: 'unknown',
      message: 'No sync status found',
      created_at: Time.current
    }
  end

  # Create new plan in Stripe and store locally
  def create_plan_in_stripe(plan_params)
    logger.info "SubscriptionPlanSyncService: Creating new plan in Stripe: #{plan_params[:name]}"
    
    unless stripe_configured?
      raise StripeConnectionError, 'Stripe is not properly configured'
    end

    # Validate required parameters
    validate_plan_creation_params(plan_params)

    begin
      # Create product first if needed
      product = create_or_find_stripe_product(plan_params)
      
      # Create price in Stripe
      stripe_price = Stripe::Price.create(
        unit_amount: plan_params[:amount],
        currency: plan_params[:currency] || 'usd',
        recurring: {
          interval: plan_params[:billing_interval],
          interval_count: plan_params[:billing_interval_count] || 1
        },
        product: product.id,
        metadata: plan_params[:metadata] || {}
      )

      # Create local record
      subscription_plan = SubscriptionPlan.create!(
        stripe_price_id: stripe_price.id,
        stripe_product_id: product.id,
        name: plan_params[:name],
        description: plan_params[:description],
        amount: stripe_price.unit_amount,
        currency: stripe_price.currency,
        billing_interval: stripe_price.recurring.interval,
        billing_interval_count: stripe_price.recurring.interval_count,
        features: plan_params[:features] || [],
        metadata: stripe_price.metadata.to_h,
        stripe_created_at: Time.at(stripe_price.created),
        last_synced_at: Time.current,
        active: plan_params.fetch(:active, true),
        legacy: plan_params.fetch(:legacy, false)
      )

      logger.info "SubscriptionPlanSyncService: Successfully created plan #{subscription_plan.stripe_price_id}"
      subscription_plan
    rescue Stripe::StripeError => e
      error_msg = "Failed to create plan in Stripe: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      raise StripeConnectionError, error_msg
    rescue ActiveRecord::RecordInvalid => e
      error_msg = "Failed to save plan locally: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      raise ValidationError, error_msg
    end
  end

  # Update local plan metadata (Stripe prices are immutable)
  def update_plan_metadata(plan, metadata_params)
    logger.info "SubscriptionPlanSyncService: Updating metadata for plan #{plan.stripe_price_id}"
    
    allowed_fields = %w[name description features active legacy]
    update_params = metadata_params.slice(*allowed_fields)
    
    if update_params.any?
      plan.update!(update_params)
      logger.info "SubscriptionPlanSyncService: Successfully updated plan #{plan.stripe_price_id}"
    else
      logger.warn "SubscriptionPlanSyncService: No valid fields to update for plan #{plan.stripe_price_id}"
    end
    
    plan
  end

  # Deactivate plan (soft delete)
  def deactivate_plan(plan)
    logger.info "SubscriptionPlanSyncService: Deactivating plan #{plan.stripe_price_id}"
    
    if plan.active_subscriptions_count > 0
      raise ValidationError, "Cannot deactivate plan with active subscriptions"
    end
    
    plan.deactivate!
    logger.info "SubscriptionPlanSyncService: Successfully deactivated plan #{plan.stripe_price_id}"
    plan
  end

  # Force sync features and other missing fields for a specific plan
  def force_sync_features_for_plan(stripe_price_id)
    logger.info "SubscriptionPlanSyncService: Force syncing features for plan #{stripe_price_id}"

    begin
      # Find the local plan
      plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)
      unless plan
        logger.error "SubscriptionPlanSyncService: Plan not found locally: #{stripe_price_id}"
        return { success: false, error: "Plan not found locally: #{stripe_price_id}" }
      end

      # Check if Stripe is configured
      unless stripe_configured?
        logger.warn "SubscriptionPlanSyncService: Stripe not configured, cannot force sync #{stripe_price_id}"
        return { success: false, error: "Stripe is not configured" }
      end

      # Fetch from Stripe
      stripe_price = Stripe::Price.retrieve(stripe_price_id)
      # Fetch the product separately if needed
      if stripe_price.product.is_a?(String)
        stripe_price.product = Stripe::Product.retrieve(stripe_price.product)
      end

      # Extract all the fields that might be missing
      extracted_features = extract_plan_features(stripe_price)
      extracted_name = extract_plan_name(stripe_price)
      extracted_description = extract_plan_description(stripe_price)

      # Log what we found
      logger.info "SubscriptionPlanSyncService: Extracted data for #{stripe_price_id}:"
      logger.info "  - Name: #{extracted_name}"
      logger.info "  - Description: #{extracted_description}"
      logger.info "  - Features: #{extracted_features}"
      logger.info "  - Active: #{stripe_price.active}"

      # Update the plan with all fields
      plan.update!(
        name: extracted_name,
        description: extracted_description,
        features: extracted_features,
        active: stripe_price.active,
        last_synced_at: Time.current
      )

      logger.info "SubscriptionPlanSyncService: Successfully force synced plan #{stripe_price_id}"
      { success: true, plan: plan, updated_fields: %w[name description features active] }

    rescue Stripe::StripeError => e
      error_msg = "Stripe error while force syncing #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, error: error_msg }
    rescue StandardError => e
      error_msg = "Error while force syncing #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      logger.error "SubscriptionPlanSyncService: Backtrace: #{e.backtrace.first(5).join(', ')}"
      { success: false, error: error_msg }
    end
  end

  # Debug method to show what would be synced for a plan
  def debug_plan_sync(stripe_price_id)
    logger.info "SubscriptionPlanSyncService: Debugging sync for plan #{stripe_price_id}"

    begin
      # Find the local plan
      local_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)

      # Check if Stripe is configured
      unless stripe_configured?
        logger.warn "SubscriptionPlanSyncService: Stripe not configured, cannot debug sync for #{stripe_price_id}"
        return { success: false, error: "Stripe is not configured" }
      end

      # Fetch from Stripe
      stripe_price = Stripe::Price.retrieve(stripe_price_id)
      # Fetch the product separately if needed
      if stripe_price.product.is_a?(String)
        stripe_price.product = Stripe::Product.retrieve(stripe_price.product)
      end

      # Extract what would be synced
      extracted_data = {
        name: extract_plan_name(stripe_price),
        description: extract_plan_description(stripe_price),
        features: extract_plan_features(stripe_price),
        amount: stripe_price.unit_amount,
        currency: stripe_price.currency,
        billing_interval: stripe_price.recurring.interval,
        billing_interval_count: stripe_price.recurring.interval_count,
        active: stripe_price.active,
        metadata: stripe_price.metadata.to_h
      }

      # Compare with local data
      comparison = {}
      if local_plan
        comparison = {
          name: { local: local_plan.name, stripe: extracted_data[:name], changed: local_plan.name != extracted_data[:name] },
          description: { local: local_plan.description, stripe: extracted_data[:description], changed: local_plan.description != extracted_data[:description] },
          features: { local: local_plan.features, stripe: extracted_data[:features], changed: local_plan.features != extracted_data[:features] },
          amount: { local: local_plan.amount, stripe: extracted_data[:amount], changed: local_plan.amount != extracted_data[:amount] },
          currency: { local: local_plan.currency, stripe: extracted_data[:currency], changed: local_plan.currency != extracted_data[:currency] },
          billing_interval: { local: local_plan.billing_interval, stripe: extracted_data[:billing_interval], changed: local_plan.billing_interval != extracted_data[:billing_interval] },
          billing_interval_count: { local: local_plan.billing_interval_count, stripe: extracted_data[:billing_interval_count], changed: local_plan.billing_interval_count != extracted_data[:billing_interval_count] },
          active: { local: local_plan.active, stripe: extracted_data[:active], changed: local_plan.active != extracted_data[:active] }
        }
      end

      {
        success: true,
        stripe_price_id: stripe_price_id,
        local_plan_exists: !local_plan.nil?,
        extracted_data: extracted_data,
        comparison: comparison,
        stripe_metadata: stripe_price.metadata.to_h,
        stripe_product_metadata: stripe_price.product.respond_to?(:metadata) ? stripe_price.product.metadata.to_h : {}
      }

    rescue Stripe::StripeError => e
      error_msg = "Stripe error while debugging #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      { success: false, error: error_msg }
    rescue StandardError => e
      error_msg = "Error while debugging #{stripe_price_id}: #{e.message}"
      logger.error "SubscriptionPlanSyncService: #{error_msg}"
      logger.error "SubscriptionPlanSyncService: Backtrace: #{e.backtrace.first(5).join(', ')}"
      { success: false, error: error_msg }
    end
  end

  private

  # Update sync status in cache for monitoring
  def update_sync_status(status, data = {})
    cache_key = "subscription_plan_sync_status:#{sync_batch_id}"
    status_data = {
      status: status,
      sync_batch_id: sync_batch_id,
      updated_at: Time.current,
      created_at: Rails.cache.read(cache_key)&.dig('created_at') || Time.current
    }.merge(data)

    Rails.cache.write(cache_key, status_data, expires_in: 24.hours)
    logger.info "SubscriptionPlanSyncService: Status updated to #{status} for batch #{sync_batch_id}"
  end

  # Check if plan was recently synced (within last hour)
  def recently_synced?(stripe_price_id)
    plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)
    return false unless plan&.last_synced_at

    plan.last_synced_at > 1.hour.ago
  end

  # Handle price webhook events
  def handle_price_webhook(price_data)
    logger.info "SubscriptionPlanSyncService: Processing price webhook for #{price_data['id']}"

    # Only process recurring prices
    return { success: true, action: :ignored, reason: 'not_recurring' } unless price_data['type'] == 'recurring'

    result = sync_plan_from_stripe_data_hash(price_data)
    action = result == :created ? :created : :updated

    {
      success: true,
      action: action,
      stripe_price_id: price_data['id'],
      sync_batch_id: sync_batch_id
    }
  end

  # Handle product webhook events
  def handle_product_webhook(product_data)
    logger.info "SubscriptionPlanSyncService: Processing product webhook for #{product_data['id']}"

    # Find all plans using this product and update them
    plans = SubscriptionPlan.where(stripe_product_id: product_data['id'])
    updated_count = 0

    plans.each do |plan|
      begin
        # Re-sync the plan to get updated product information
        sync_plan_from_stripe(plan.stripe_price_id)
        updated_count += 1
      rescue StandardError => e
        logger.error "SubscriptionPlanSyncService: Failed to update plan #{plan.stripe_price_id}: #{e.message}"
      end
    end

    {
      success: true,
      action: :updated,
      stripe_product_id: product_data['id'],
      updated_plans_count: updated_count,
      sync_batch_id: sync_batch_id
    }
  end

  # Sync plan from Stripe price data hash (for webhooks)
  def sync_plan_from_stripe_data_hash(price_data)
    existing_plan = SubscriptionPlan.find_by(stripe_price_id: price_data['id'])

    if existing_plan
      update_existing_plan_from_hash(existing_plan, price_data)
      :updated
    else
      create_new_plan_from_hash(price_data)
      :created
    end
  end

  # Update existing plan from webhook price data
  def update_existing_plan_from_hash(plan, price_data)
    # Extract features from webhook data
    features = []
    if price_data['metadata'] && price_data['metadata']['features'].present?
      features = JSON.parse(price_data['metadata']['features']) rescue []
    end
    features = [] unless features.is_a?(Array)

    # Preserve existing metadata if webhook metadata is empty, otherwise merge
    updated_metadata = if price_data['metadata'].blank?
      plan.metadata # Keep existing metadata
    else
      plan.metadata.merge(price_data['metadata']) # Merge with webhook metadata
    end

    plan.update!(
      name: price_data['metadata']&.dig('name') || plan.name || "Plan #{price_data['id']}",
      description: price_data['metadata']&.dig('description') || plan.description || "Subscription plan",
      amount: price_data['unit_amount'],
      currency: price_data['currency'],
      billing_interval: price_data.dig('recurring', 'interval'),
      billing_interval_count: price_data.dig('recurring', 'interval_count'),
      features: features,
      metadata: updated_metadata,
      stripe_created_at: Time.at(price_data['created']),
      last_synced_at: Time.current,
      active: price_data['active']
    )
  end

  # Create new plan from webhook price data
  def create_new_plan_from_hash(price_data)
    is_legacy = legacy_price_id?(price_data['id'])

    SubscriptionPlan.create!(
      stripe_price_id: price_data['id'],
      stripe_product_id: price_data['product'],
      name: "Plan #{price_data['id']}", # Will be updated when product info is available
      description: "Subscription plan",
      amount: price_data['unit_amount'],
      currency: price_data['currency'],
      billing_interval: price_data.dig('recurring', 'interval'),
      billing_interval_count: price_data.dig('recurring', 'interval_count'),
      features: [],
      metadata: price_data['metadata'] || { 'synced_from' => 'webhook', 'synced_at' => Time.current.iso8601 },
      stripe_created_at: Time.at(price_data['created']),
      last_synced_at: Time.current,
      active: price_data['active'],
      legacy: is_legacy
    )
  end

  # Check if Stripe is properly configured
  def stripe_configured?
    Stripe.api_key.present?
  rescue StandardError
    false
  end

  # Fetch all prices from Stripe
  def fetch_all_stripe_prices
    prices = Stripe::Price.list(limit: 100).auto_paging_each.to_a

    # Fetch products separately for each price
    prices.each do |price|
      if price.product.is_a?(String)
        begin
          price.product = Stripe::Product.retrieve(price.product)
        rescue Stripe::StripeError => e
          logger.warn "SubscriptionPlanSyncService: Failed to fetch product #{price.product} for price #{price.id}: #{e.message}"
        end
      end
    end

    prices
  end

  # Sync plan from Stripe price data
  def sync_plan_from_stripe_data(stripe_price)
    # Skip if not a subscription price
    return :skipped unless stripe_price.type == 'recurring'

    existing_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price.id)
    
    if existing_plan
      update_existing_plan(existing_plan, stripe_price)
      :updated
    else
      create_new_plan_from_stripe(stripe_price)
      :created
    end
  end

  # Update existing plan with Stripe data
  def update_existing_plan(plan, stripe_price)
    # Preserve existing metadata if Stripe metadata is empty, otherwise merge
    updated_metadata = if stripe_price.metadata.to_h.empty?
      plan.metadata # Keep existing metadata
    else
      plan.metadata.merge(stripe_price.metadata.to_h) # Merge with Stripe metadata
    end

    plan.update!(
      name: extract_plan_name(stripe_price),
      description: extract_plan_description(stripe_price),
      amount: stripe_price.unit_amount,
      currency: stripe_price.currency,
      billing_interval: stripe_price.recurring.interval,
      billing_interval_count: stripe_price.recurring.interval_count,
      features: extract_plan_features(stripe_price),
      metadata: updated_metadata,
      stripe_created_at: Time.at(stripe_price.created),
      last_synced_at: Time.current,
      active: stripe_price.active
    )
  end

  # Create new plan from Stripe data
  def create_new_plan_from_stripe(stripe_price)
    # Determine if this is a legacy plan based on known price IDs
    is_legacy = legacy_price_id?(stripe_price.id)
    
    SubscriptionPlan.create!(
      stripe_price_id: stripe_price.id,
      stripe_product_id: stripe_price.product.is_a?(String) ? stripe_price.product : stripe_price.product.id,
      name: extract_plan_name(stripe_price),
      description: extract_plan_description(stripe_price),
      amount: stripe_price.unit_amount,
      currency: stripe_price.currency,
      billing_interval: stripe_price.recurring.interval,
      billing_interval_count: stripe_price.recurring.interval_count,
      features: extract_plan_features(stripe_price),
      metadata: stripe_price.metadata.to_h.presence || { 'synced_from' => 'stripe', 'synced_at' => Time.current.iso8601 },
      stripe_created_at: Time.at(stripe_price.created),
      last_synced_at: Time.current,
      active: stripe_price.active,
      legacy: is_legacy
    )
  end

  # Helper methods for extracting plan data
  def extract_plan_name(stripe_price)
    # Try to get name from product or metadata
    name = if stripe_price.product.respond_to?(:name)
      stripe_price.product.name
    elsif stripe_price.metadata['name'].present?
      stripe_price.metadata['name']
    else
      "Plan #{stripe_price.id}"
    end

    # Ensure we return a string, not an array
    name.is_a?(Array) ? name.join(', ') : name.to_s
  end

  def extract_plan_description(stripe_price)
    description = if stripe_price.product.respond_to?(:description)
      stripe_price.product.description
    elsif stripe_price.metadata['description'].present?
      stripe_price.metadata['description']
    else
      "Subscription plan"
    end

    # Ensure we return a string, not an array
    description.is_a?(Array) ? description.join(', ') : description.to_s
  end

  def extract_plan_features(stripe_price)
    # Extract features from metadata or product metadata
    features = []

    # Try price metadata first
    if stripe_price.metadata['features'].present?
      begin
        features = JSON.parse(stripe_price.metadata['features'])
        logger.debug "SubscriptionPlanSyncService: Extracted features from price metadata for #{stripe_price.id}: #{features}"
      rescue JSON::ParserError => e
        logger.warn "SubscriptionPlanSyncService: Failed to parse features from price metadata for #{stripe_price.id}: #{e.message}"
        features = []
      end
    # Try product metadata if price metadata doesn't have features
    elsif stripe_price.product.respond_to?(:metadata) && stripe_price.product.metadata['features'].present?
      begin
        features = JSON.parse(stripe_price.product.metadata['features'])
        logger.debug "SubscriptionPlanSyncService: Extracted features from product metadata for #{stripe_price.id}: #{features}"
      rescue JSON::ParserError => e
        logger.warn "SubscriptionPlanSyncService: Failed to parse features from product metadata for #{stripe_price.id}: #{e.message}"
        features = []
      end
    # Try marketing_features field in product metadata (common Stripe pattern)
    elsif stripe_price.product.respond_to?(:metadata) && stripe_price.product.metadata['marketing_features'].present?
      begin
        features = JSON.parse(stripe_price.product.metadata['marketing_features'])
        logger.debug "SubscriptionPlanSyncService: Extracted features from product marketing_features for #{stripe_price.id}: #{features}"
      rescue JSON::ParserError => e
        logger.warn "SubscriptionPlanSyncService: Failed to parse marketing_features from product metadata for #{stripe_price.id}: #{e.message}"
        features = []
      end
    # Try the direct marketing_features field on the product (Stripe's native field)
    elsif stripe_price.product.respond_to?(:marketing_features) && stripe_price.product.marketing_features.present?
      begin
        # marketing_features is an array of objects with 'name' property
        features = stripe_price.product.marketing_features.map { |feature| feature["name"] || feature[:name] }.compact
        logger.debug "SubscriptionPlanSyncService: Extracted features from product marketing_features field for #{stripe_price.id}: #{features}"
      rescue => e
        logger.warn "SubscriptionPlanSyncService: Failed to extract marketing_features from product for #{stripe_price.id}: #{e.message}"
        features = []
      end
    else
      logger.debug "SubscriptionPlanSyncService: No features found in metadata or marketing_features for #{stripe_price.id}"
    end

    # Ensure we return an array
    result = features.is_a?(Array) ? features : []
    logger.info "SubscriptionPlanSyncService: Final features for #{stripe_price.id}: #{result}" if result.any?
    result
  end

  def legacy_price_id?(price_id)
    # Known legacy price IDs
    %w[price_1R9Q66DYYVPVcCCrnqiXNafF].include?(price_id)
  end

  def validate_plan_creation_params(params)
    required_fields = %w[name amount billing_interval]
    missing_fields = required_fields.select { |field| params[field.to_sym].blank? }
    
    if missing_fields.any?
      raise ValidationError, "Missing required fields: #{missing_fields.join(', ')}"
    end
  end

  def create_or_find_stripe_product(plan_params)
    # Try to find existing product or create new one
    if plan_params[:stripe_product_id].present?
      Stripe::Product.retrieve(plan_params[:stripe_product_id])
    else
      Stripe::Product.create(
        name: plan_params[:name],
        description: plan_params[:description],
        metadata: plan_params[:metadata] || {}
      )
    end
  end
end
