# frozen_string_literal: true

# == Schema Information
#
# Table name: subscription_plans
#
#  id                     :bigint           not null, primary key
#  active                 :boolean          default(TRUE), not null
#  amount                 :integer
#  available_for_scout    :boolean          default(FALSE), not null
#  available_for_talent   :boolean          default(FALSE), not null
#  billing_interval       :string
#  billing_interval_count :integer
#  currency               :string
#  description            :text
#  features               :jsonb            not null
#  last_synced_at         :datetime
#  legacy                 :boolean          default(FALSE), not null
#  metadata               :jsonb            not null
#  name                   :string           not null
#  stripe_created_at      :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  stripe_price_id        :string
#  stripe_product_id      :string
#
# Indexes
#
#  index_subscription_plans_on_active                (active)
#  index_subscription_plans_on_active_and_legacy     (active,legacy)
#  index_subscription_plans_on_available_for_scout   (available_for_scout)
#  index_subscription_plans_on_available_for_talent  (available_for_talent)
#  index_subscription_plans_on_billing_interval      (billing_interval)
#  index_subscription_plans_on_legacy                (legacy)
#  index_subscription_plans_on_section_availability  (available_for_scout,available_for_talent)
#  index_subscription_plans_on_stripe_price_id       (stripe_price_id) UNIQUE WHERE (stripe_price_id IS NOT NULL)
#
class SubscriptionPlan < ApplicationRecord
  # Associations
  # Note: Pay::Subscription uses processor_plan field to store Stripe price ID
  # The Pay gem doesn't have a 'processor' column, so we don't need to filter by it
  has_many :subscriptions,
           class_name: 'Pay::Subscription',
           foreign_key: 'processor_plan',
           primary_key: 'stripe_price_id'

  # Validations
  validates :stripe_price_id, uniqueness: true, allow_blank: true
  validates :stripe_price_id, presence: true, if: -> { !stripe_price_id.nil? && stripe_price_id.blank? }
  validates :name, presence: true
  # Pricing validations - only required when Stripe Price ID is present (Stripe-first architecture)
  validates :amount,
            presence: { if: :stripe_price_id? },
            numericality: { greater_than: 0, allow_blank: true }
  validates :currency,
            presence: { if: :stripe_price_id? },
            inclusion: { in: %w[usd eur gbp], allow_blank: true }
  validates :billing_interval,
            presence: { if: :recurring_plan? },
            inclusion: {
              in: %w[day week month year],
              allow_blank: true
            }
  validates :billing_interval_count,
            presence: { if: :recurring_plan? },
            numericality: {
              greater_than: 0,
              allow_blank: true
            }
  validate :features_must_be_array
  validates :metadata, presence: true

  # Callbacks
  before_validation :set_default_metadata, if: -> { metadata.blank? }
  before_validation :set_default_features, if: -> { features.nil? && new_record? }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :legacy, -> { where(legacy: true) }
  scope :current, -> { where(legacy: false) }
  scope :available_for_signup, -> { active.current }
  scope :available_for_new_subscriptions, -> { active.current }
  scope :monthly, -> { where(billing_interval: 'month') }
  scope :yearly, -> { where(billing_interval: 'year') }
  scope :by_amount, -> { order(:amount) }
  scope :recently_synced, -> { where('last_synced_at > ?', 1.hour.ago) }
  scope :needs_sync,
        -> { where('last_synced_at IS NULL OR last_synced_at < ?', 1.day.ago) }

  # Section availability scopes
  scope :available_for_scout, -> { where(available_for_scout: true) }
  scope :available_for_talent, -> { where(available_for_talent: true) }
  scope :available_for_any_section, -> { where('available_for_scout = ? OR available_for_talent = ?', true, true) }
  scope :not_assigned_to_sections, -> { where(available_for_scout: false, available_for_talent: false) }

  # Section availability scopes
  scope :available_for_scout, -> { where(available_for_scout: true) }
  scope :available_for_talent, -> { where(available_for_talent: true) }
  scope :available_for_any_section, -> { where('available_for_scout = ? OR available_for_talent = ?', true, true) }
  scope :not_assigned_to_sections, -> { where(available_for_scout: false, available_for_talent: false) }

  # Class methods
  def self.find_by_stripe_price_id(price_id)
    find_by(stripe_price_id: price_id)
  end

  def self.find_by_stripe_price_id!(price_id)
    find_by!(stripe_price_id: price_id)
  end

  # Instance methods
  def formatted_amount
    return "Contact us" if amount.nil?

    case currency&.downcase
    when 'usd'
      "$#{sprintf('%.2f', amount / 100.0)}"
    when 'eur'
      "€#{sprintf('%.2f', amount / 100.0)}"
    when 'gbp'
      "£#{sprintf('%.2f', amount / 100.0)}"
    else
      "#{sprintf('%.2f', amount / 100.0)} #{currency&.upcase}"
    end
  end

  # Alias for compatibility with tests
  alias_method :display_amount, :formatted_amount

  def billing_cycle_description
    if billing_interval_count == 1
      billing_interval.capitalize
    else
      "Every #{billing_interval_count} #{billing_interval.pluralize}"
    end
  end

  def premium?
    # Check if this is the premium plan
    stripe_price_id == 'price_1R9Q66DYYVPVcCCrnqiXNafF'
  end

  def standard?
    # Standard plan check
    stripe_price_id == 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
  end

  def monthly?
    billing_interval == 'month'
  end

  def yearly?
    billing_interval == 'year'
  end

  def needs_sync?
    last_synced_at.nil? || last_synced_at < 1.day.ago
  end

  def sync_status
    return 'never_synced' if last_synced_at.nil?
    return 'recently_synced' if last_synced_at > 1.hour.ago
    return 'needs_sync' if last_synced_at < 1.day.ago

    'synced'
  end



  def can_be_deleted?
    !active? && active_subscriptions_count == 0
  end

  def mark_as_synced!
    update!(last_synced_at: Time.current)
  end

  def deactivate!
    update!(active: false)
  end

  def activate!
    update!(active: true)
  end

  # For admin interface display
  def display_name
    name
  end

  def status_badge_class
    if active?
      legacy? ? 'admin-badge-warning' : 'admin-badge-success'
    else
      'admin-badge-secondary'
    end
  end

  def status_text
    if active?
      legacy? ? 'Active (Legacy)' : 'Active'
    else
      'Inactive'
    end
  end

  # Section assignment helper methods
  def assigned_sections
    sections = []
    sections << 'Scout' if available_for_scout?
    sections << 'Talent' if available_for_talent?
    sections
  end

  def assigned_sections_text
    sections = assigned_sections
    return 'Not assigned' if sections.empty?
    sections.join(', ')
  end

  def available_for_any_section?
    available_for_scout? || available_for_talent?
  end

  def assign_to_sections!(sections)
    update!(
      available_for_scout: sections.include?('scout'),
      available_for_talent: sections.include?('talent')
    )
  end

  # Subscription statistics methods
  def subscription_count
    subscriptions.count
  end

  def active_subscriptions_count
    subscriptions.where(status: ['active', 'trialing']).count
  end

  def total_revenue
    # Calculate total revenue from all charges for this plan's subscriptions
    subscription_ids = subscriptions.pluck(:id)
    return formatted_currency(0) if subscription_ids.empty?

    # Note: Pay::Charge table doesn't have processor column, but we can filter by subscription_id
    # Since all our subscriptions are Stripe-based, we don't need to filter by processor
    total_cents = Pay::Charge.where(subscription_id: subscription_ids)
                             .sum(:amount)

    formatted_currency(total_cents)
  end

  def recent_subscriptions(limit = 5)
    subscriptions.includes(customer: :owner)
                 .order(created_at: :desc)
                 .limit(limit)
  end

  private

  # Check if this plan represents a recurring subscription (vs one-time payment)
  def recurring_plan?
    stripe_price_id? && billing_interval.present?
  end

  def formatted_currency(amount_in_cents)
    currency_symbol = case currency.downcase
                     when 'usd' then '$'
                     when 'eur' then '€'
                     when 'gbp' then '£'
                     else currency.upcase
                     end

    "#{currency_symbol}#{sprintf('%.2f', amount_in_cents / 100.0)}"
  end

  def set_default_metadata
    self.metadata = {
      'created_via' => 'admin_interface',
      'created_at' => Time.current.iso8601
    }
  end

  def set_default_features
    self.features = ['Basic features']
  end

  def features_must_be_array
    if features.nil?
      errors.add(:features, "can't be nil")
    elsif !features.is_a?(Array)
      errors.add(:features, 'must be an array')
    end
  end
end
