<% content_for :title, "Subscription Plans" %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Subscription Plans</h1>
        <p class="mt-2 text-sm text-stone-700">Manage subscription plans with Stripe-first architecture</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
        <%= link_to sync_all_super_admin_subscription_plans_path,
            class: "inline-flex items-center justify-center rounded-md border border-stone-300 px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2",
            data: { turbo_method: :post, confirm: "This will sync all plans with Stripe. Continue?" } do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Sync All with Stripe
        <% end %>
        <%= link_to new_super_admin_subscription_plan_path,
            class: "inline-flex items-center justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          New Plan
        <% end %>
      </div>
    </div>

    <!-- Stripe-First Architecture Notice -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start">
        <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Stripe-First Architecture</h3>
          <p class="mt-1 text-sm text-blue-700">
            This system uses Stripe as the source of truth for pricing. Plans sync pricing data from Stripe automatically.
            To change pricing, create new prices in Stripe and update the plan's Stripe Price ID.
          </p>
        </div>
      </div>
    </div>

    <!-- Search and Filters Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search and Action Buttons Row -->
        <div class="flex items-end gap-4">
          <!-- Search Field (takes most space) -->
          <div class="flex-1">
            <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :search,
                value: params[:search],
                placeholder: "Search by name, description, price ID...",
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Filter and Clear Buttons -->
          <div class="flex items-center space-x-4">
            <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
            <% if params[:search].present? || params[:status].present? || params[:billing_interval].present? || params[:sync_status].present? %>
              <%= link_to "Clear", super_admin_subscription_plans_path, class: "text-stone-600 hover:text-stone-900" %>
            <% end %>
          </div>
        </div>

        <!-- Filter Dropdowns Row -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <!-- Status Filter -->
          <div>
            <%= form.label :status, "Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :status,
                options_for_select(@filter_options[:status], params[:status]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Billing Interval Filter -->
          <div>
            <%= form.label :billing_interval, "Billing Interval", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :billing_interval,
                options_for_select(@filter_options[:billing_interval], params[:billing_interval]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Sync Status Filter -->
          <div>
            <%= form.label :sync_status, "Sync Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :sync_status,
                options_for_select(@filter_options[:sync_status], params[:sync_status]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Results Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <% if @subscription_plans.any? %>
        <!-- Results Summary -->
        <div class="mb-4 flex items-center justify-between">
          <p class="text-sm text-stone-700">
            Showing <%= @pagy.from %>-<%= @pagy.to %> of <%= @pagy.count %> plans
          </p>
          <div class="flex items-center space-x-4">
            <!-- Sort Options -->
            <%= form_with url: request.path, method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
              <%= form.hidden_field :search, value: params[:search] %>
              <%= form.hidden_field :status, value: params[:status] %>
              <%= form.hidden_field :billing_interval, value: params[:billing_interval] %>
              <%= form.hidden_field :sync_status, value: params[:sync_status] %>
              <%= form.label :sort_by, "Sort by:", class: "text-sm text-stone-700" %>
              <%= form.select :sort_by,
                  options_for_select([
                    ['Name', 'name'],
                    ['Amount', 'amount'],
                    ['Created Date', 'created_at'],
                    ['Last Synced', 'last_synced_at']
                  ], params[:sort_by]),
                  {},
                  { class: "rounded-md border-stone-300 text-sm", onchange: "this.form.submit();" } %>
              <%= form.select :sort_direction,
                  options_for_select([
                    ['Ascending', 'asc'],
                    ['Descending', 'desc']
                  ], params[:sort_direction]),
                  {},
                  { class: "rounded-md border-stone-300 text-sm", onchange: "this.form.submit();" } %>
            <% end %>
          </div>
        </div>

        <!-- Table -->
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-stone-300">
            <thead class="bg-stone-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Plan</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Billing</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Sync Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Subscriptions</th>
                <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-stone-200">
              <% @subscription_plans.each do |plan| %>
                <tr class="hover:bg-stone-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div>
                        <div class="text-sm font-medium text-stone-900">
                          <%= link_to plan.name, super_admin_subscription_plan_path(plan), class: "hover:text-stone-600" %>
                        </div>
                        <div class="text-sm text-stone-500"><%= plan.stripe_price_id %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-stone-900"><%= plan.formatted_amount %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-stone-900"><%= plan.billing_cycle_description %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= plan.active? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                      <%= plan.status_text %>
                    </span>
                    <% if plan.legacy? %>
                      <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                        Legacy
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= plan.sync_status == 'recently_synced' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                      <%= plan.sync_status.humanize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                    <%= plan.subscriptions.count %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <%= link_to "View", super_admin_subscription_plan_path(plan), class: "text-stone-600 hover:text-stone-900" %>
                    <%= link_to "Edit", edit_super_admin_subscription_plan_path(plan), class: "text-stone-600 hover:text-stone-900" %>
                    <% if plan.stripe_price_id.present? %>
                      <%= link_to "Sync", sync_plan_super_admin_subscription_plan_path(plan), class: "text-stone-600 hover:text-stone-900", data: { turbo_method: :post, confirm: "Sync this plan with Stripe?" } %>
                    <% else %>
                      <span class="text-stone-400 cursor-not-allowed">No Sync</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
          <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
        </div>
      <% else %>
        <!-- Empty State -->
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No subscription plans found</h3>
          <p class="mt-1 text-sm text-stone-500">Get started by creating a new subscription plan.</p>
          <div class="mt-6">
            <%= link_to new_super_admin_subscription_plan_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
              <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              New Subscription Plan
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
