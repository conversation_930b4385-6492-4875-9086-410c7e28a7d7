<% content_for :title, "Edit #{@subscription_plan.name}" %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Edit <%= @subscription_plan.name %></h1>
        <p class="mt-2 text-sm text-stone-700">Update subscription plan details</p>
      </div>
    </div>

    <!-- Back Button -->
    <div class="mt-6 pt-6 border-t border-stone-200">
      <div class="flex justify-center">
        <%= link_to super_admin_subscription_plan_path(@subscription_plan), class: "text-stone-600 hover:text-stone-900 text-sm" do %>
          ← Back to Plan Details
        <% end %>
      </div>
    </div>

    <!-- Form Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <%= form_with model: [:super_admin, @subscription_plan], local: true, class: "space-y-6" do |form| %>
        <% if @subscription_plan.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                  <% @subscription_plan.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Basic Information -->
        <div class="bg-stone-50 rounded-lg p-6">
          <h2 class="text-lg font-medium text-stone-900 mb-4">Basic Information</h2>
          
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <%= form.label :name, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_field :name,
                  class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm",
                  placeholder: "e.g., Standard Plan" %>
            </div>

            <div>
              <%= form.label :stripe_price_id, "Stripe Price ID", class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_field :stripe_price_id,
                  class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm font-mono",
                  placeholder: "price_1234567890abcdef" %>
              <p class="mt-1 text-xs text-stone-500">Changing this will affect Stripe integration</p>
            </div>
          </div>

          <div class="mt-6">
            <%= form.label :description, class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_area :description,
                rows: 3,
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm",
                placeholder: "Describe what this plan includes..." %>
          </div>
        </div>

        <!-- Pricing Information (Read-Only) -->
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-amber-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-amber-800">Pricing Managed in Stripe</h3>
              <p class="mt-1 text-sm text-amber-700">
                Pricing information (amount, currency, billing interval) is managed in Stripe and cannot be edited here.
                To change pricing, create a new price in Stripe and update the Stripe Price ID above.
              </p>
            </div>
          </div>

          <div class="mt-4 bg-white rounded-md p-4 border border-amber-200">
            <h4 class="text-sm font-medium text-stone-900 mb-3">Current Pricing (from Stripe)</h4>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Amount</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @subscription_plan.formatted_amount %></dd>
              </div>
              <div>
                <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Currency</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @subscription_plan.currency.upcase %></dd>
              </div>
              <div>
                <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Billing</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @subscription_plan.billing_cycle_description %></dd>
              </div>
            </div>
          </div>

          <% if @subscription_plan.stripe_price_id.present? %>
            <div class="mt-4 flex items-center justify-between">
              <p class="text-xs text-amber-700">
                Last synced: <%= @subscription_plan.last_synced_at ? time_ago_in_words(@subscription_plan.last_synced_at) + " ago" : "Never" %>
              </p>
              <%= link_to sync_plan_super_admin_subscription_plan_path(@subscription_plan),
                  class: "inline-flex items-center px-3 py-1.5 border border-amber-300 text-xs font-medium rounded text-amber-700 bg-amber-100 hover:bg-amber-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500",
                  data: { turbo_method: :post, confirm: "Sync pricing data from Stripe?" } do %>
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Sync from Stripe
              <% end %>
            </div>
          <% end %>
        </div>

        <!-- Status Settings -->
        <div class="bg-stone-50 rounded-lg p-6">
          <h2 class="text-lg font-medium text-stone-900 mb-4">Status Settings</h2>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <%= form.check_box :active,
                  class: "h-4 w-4 text-stone-600 focus:ring-stone-500 border-stone-300 rounded" %>
              <%= form.label :active, "Active", class: "ml-2 block text-sm text-stone-900" %>
              <p class="ml-6 text-xs text-stone-500">Active plans can be used for new subscriptions</p>
            </div>

            <div class="flex items-center">
              <%= form.check_box :legacy,
                  class: "h-4 w-4 text-stone-600 focus:ring-stone-500 border-stone-300 rounded" %>
              <%= form.label :legacy, "Legacy", class: "ml-2 block text-sm text-stone-900" %>
              <p class="ml-6 text-xs text-stone-500">Legacy plans are not available for new signups</p>
            </div>
          </div>
        </div>

        <!-- Features -->
        <div class="bg-stone-50 rounded-lg p-6">
          <h2 class="text-lg font-medium text-stone-900 mb-4">Features</h2>
          
          <div id="features-container">
            <% if @subscription_plan.features.present? %>
              <% @subscription_plan.features.each_with_index do |feature, index| %>
                <div class="feature-input flex items-center space-x-2 mb-2">
                  <%= text_field_tag "subscription_plan[features][]", feature,
                      class: "flex-1 rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm",
                      placeholder: "Enter a feature..." %>
                  <button type="button" class="remove-feature text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              <% end %>
            <% else %>
              <div class="feature-input flex items-center space-x-2 mb-2">
                <%= text_field_tag "subscription_plan[features][]", "",
                    class: "flex-1 rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm",
                    placeholder: "Enter a feature..." %>
                <button type="button" class="remove-feature text-red-600 hover:text-red-800">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            <% end %>
          </div>
          
          <button type="button" id="add-feature" class="mt-2 inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Feature
          </button>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-stone-200">
          <%= link_to "Cancel", super_admin_subscription_plan_path(@subscription_plan),
              class: "text-stone-600 hover:text-stone-900 text-sm" %>
          <%= form.submit "Update Subscription Plan",
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const addFeatureBtn = document.getElementById('add-feature');
  const featuresContainer = document.getElementById('features-container');

  addFeatureBtn.addEventListener('click', function() {
    const featureInput = document.createElement('div');
    featureInput.className = 'feature-input flex items-center space-x-2 mb-2';
    featureInput.innerHTML = `
      <input type="text" name="subscription_plan[features][]" 
             class="flex-1 rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm"
             placeholder="Enter a feature...">
      <button type="button" class="remove-feature text-red-600 hover:text-red-800">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
      </button>
    `;
    featuresContainer.appendChild(featureInput);
  });

  featuresContainer.addEventListener('click', function(e) {
    if (e.target.closest('.remove-feature')) {
      e.target.closest('.feature-input').remove();
    }
  });
});
</script>
