# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: subscription_plans
#
#  id                     :bigint           not null, primary key
#  active                 :boolean          default(TRUE), not null
#  amount                 :integer
#  billing_interval       :string
#  billing_interval_count :integer
#  currency               :string
#  description            :text
#  features               :jsonb            not null
#  last_synced_at         :datetime
#  legacy                 :boolean          default(FALSE), not null
#  metadata               :jsonb            not null
#  name                   :string           not null
#  stripe_created_at      :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  stripe_price_id        :string
#  stripe_product_id      :string
#
# Indexes
#
#  index_subscription_plans_on_active             (active)
#  index_subscription_plans_on_active_and_legacy  (active,legacy)
#  index_subscription_plans_on_billing_interval   (billing_interval)
#  index_subscription_plans_on_legacy             (legacy)
#  index_subscription_plans_on_stripe_price_id    (stripe_price_id) UNIQUE WHERE (stripe_price_id IS NOT NULL)
#
one:
  stripe_price_id: price_1R9Q55DYYVPVcCCrWQOwsKmT
  stripe_product_id: prod_test_standard
  name: Standard Plan
  description: Standard subscription plan for testing
  amount: 2900
  currency: usd
  billing_interval: month
  billing_interval_count: 1
  active: true
  legacy: false
  features: '{"feature1": true, "feature2": false}'
  metadata: '{"test": true}'
  stripe_created_at: 2025-07-06 16:10:11
  last_synced_at: 2025-07-06 16:10:11

two:
  stripe_price_id: price_1R9Q66DYYVPVcCCrnqiXNafF
  stripe_product_id: prod_test_premium
  name: Premium Plan
  description: Premium subscription plan for testing
  amount: 4900
  currency: usd
  billing_interval: month
  billing_interval_count: 1
  active: true
  legacy: false
  features: '{"feature1": true, "feature2": true, "premium_feature": true}'
  metadata: '{"test": true, "premium": true}'
  stripe_created_at: 2025-07-06 16:10:11
  last_synced_at: 2025-07-06 16:10:11
