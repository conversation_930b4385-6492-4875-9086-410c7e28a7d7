# == Schema Information
#
# Table name: subscription_plans
#
#  id                     :bigint           not null, primary key
#  active                 :boolean          default(TRUE), not null
#  amount                 :integer
#  billing_interval       :string
#  billing_interval_count :integer
#  currency               :string
#  description            :text
#  features               :jsonb            not null
#  last_synced_at         :datetime
#  legacy                 :boolean          default(FALSE), not null
#  metadata               :jsonb            not null
#  name                   :string           not null
#  stripe_created_at      :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  stripe_price_id        :string
#  stripe_product_id      :string
#
# Indexes
#
#  index_subscription_plans_on_active             (active)
#  index_subscription_plans_on_active_and_legacy  (active,legacy)
#  index_subscription_plans_on_billing_interval   (billing_interval)
#  index_subscription_plans_on_legacy             (legacy)
#  index_subscription_plans_on_stripe_price_id    (stripe_price_id) UNIQUE WHERE (stripe_price_id IS NOT NULL)
#
require 'test_helper'

class SubscriptionPlanTest < ActiveSupport::TestCase
  def setup
    @standard_plan = subscription_plans(:one)
    @premium_plan = subscription_plans(:two)
  end

  # Validation tests
  test 'should be valid with valid attributes' do
    plan =
      SubscriptionPlan.new(
        name: 'Test Plan',
        description: 'Test description',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
      )
    assert plan.valid?
  end

  test 'should require name' do
    plan =
      SubscriptionPlan.new(
        description: 'Test description',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
      )
    assert_not plan.valid?
    assert_includes plan.errors[:name], "can't be blank"
  end

  test 'should set default features when blank' do
    plan =
      SubscriptionPlan.new(
        name: 'Test Plan',
        description: 'Test description',
        metadata: {
          'test' => true,
        },
      )

    # Features should be automatically set by callback
    assert plan.valid?
    assert_not_nil plan.features
  end

  test 'should set default metadata when blank' do
    plan =
      SubscriptionPlan.new(
        name: 'Test Plan',
        description: 'Test description',
        features: {
          'feature1' => true,
        },
      )

    # Metadata should be automatically set by callback
    assert plan.valid?
    assert_not_nil plan.metadata
  end

  test 'should require stripe_price_id when present but empty' do
    plan =
      SubscriptionPlan.new(
        name: 'Test Plan',
        description: 'Test description',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
        stripe_price_id: '',
      )
    assert_not plan.valid?
    assert_includes plan.errors[:stripe_price_id], "can't be blank"
  end

  test 'should allow nil stripe_price_id' do
    plan =
      SubscriptionPlan.new(
        name: 'Test Plan',
        description: 'Test description',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
        stripe_price_id: nil,
      )
    assert plan.valid?
  end

  # Scope tests
  test 'active scope returns only active plans' do
    active_plans = SubscriptionPlan.active
    assert_includes active_plans, @standard_plan
    assert_includes active_plans, @premium_plan

    # Create inactive plan
    inactive_plan =
      SubscriptionPlan.create!(
        name: 'Inactive Plan',
        description: 'Inactive plan',
        features: {
          'feature1' => false,
        },
        metadata: {
          'test' => true,
        },
        active: false,
      )

    active_plans = SubscriptionPlan.active
    assert_not_includes active_plans, inactive_plan
  end

  test 'legacy scope returns only legacy plans' do
    legacy_plans = SubscriptionPlan.legacy
    assert_empty legacy_plans

    # Create legacy plan
    legacy_plan =
      SubscriptionPlan.create!(
        name: 'Legacy Plan',
        description: 'Legacy plan',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
        legacy: true,
      )

    legacy_plans = SubscriptionPlan.legacy
    assert_includes legacy_plans, legacy_plan
  end

  test 'current scope returns active non-legacy plans' do
    current_plans = SubscriptionPlan.current
    assert_includes current_plans, @standard_plan
    assert_includes current_plans, @premium_plan

    # Create legacy plan
    legacy_plan =
      SubscriptionPlan.create!(
        name: 'Legacy Plan',
        description: 'Legacy plan',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
        legacy: true,
      )

    current_plans = SubscriptionPlan.current
    assert_not_includes current_plans, legacy_plan
  end

  test 'available_for_new_subscriptions scope returns active non-legacy plans' do
    available_plans = SubscriptionPlan.available_for_new_subscriptions
    assert_includes available_plans, @standard_plan
    assert_includes available_plans, @premium_plan
  end

  # Business logic tests
  test 'premium? returns true for premium plan' do
    assert @premium_plan.premium?
  end

  test 'premium? returns false for standard plan' do
    assert_not @standard_plan.premium?
  end

  test 'standard? returns true for standard plan' do
    assert @standard_plan.standard?
  end

  test 'standard? returns false for premium plan' do
    assert_not @premium_plan.standard?
  end

  test 'monthly? returns true for monthly billing' do
    assert @standard_plan.monthly?
    assert @premium_plan.monthly?
  end

  test 'yearly? returns false for monthly plans' do
    assert_not @standard_plan.yearly?
    assert_not @premium_plan.yearly?
  end

  test 'display_amount returns formatted amount' do
    assert_equal '$29.00', @standard_plan.display_amount
    assert_equal '$49.00', @premium_plan.display_amount
  end

  test "display_amount returns 'Contact us' for nil amount" do
    plan =
      SubscriptionPlan.new(
        name: 'Custom Plan',
        description: 'Custom plan',
        features: {
          'feature1' => true,
        },
        metadata: {
          'test' => true,
        },
        amount: nil,
      )
    assert_equal 'Contact us', plan.display_amount
  end
end
