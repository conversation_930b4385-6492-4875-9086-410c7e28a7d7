# Use this file to easily define all of your cron jobs.
# Learn more: http://github.com/javan/whenever

# Set environment to Rails environment
set :environment, ENV['RAILS_ENV'] || 'development'

# Set output log file
set :output, 'log/cron.log'

# Schedule ExpireJobsJob to run daily at 3:00 AM server time
every 1.day, at: '3:00 am' do
  # Use runner to execute the job's perform_later method
  # This assumes you have a background job adapter configured (like Sidekiq, SolidQueue, etc.)
  runner 'ExpireJobsJob.perform_later'

  # If you aren't using a background job adapter or want direct execution (not recommended for long tasks):
  # runner "ExpireJobsJob.perform_now"

  # Alternatively, if you created a Rake task:
  # rake "jobs:expire"
end

# Schedule SubscriptionPlanSyncJob to run daily at 2:00 AM server time
# Runs before ExpireJobsJob to ensure subscription data is up to date
every 1.day, at: '2:00 am' do
  # Full sync from Stripe to catch any missed webhook events
  runner 'SubscriptionPlanSyncJob.perform_later(sync_type: "full", force_sync: false)'
end

# Schedule health check sync to run every 6 hours
# This ensures data consistency without full sync overhead
every 6.hours do
  runner 'SubscriptionPlanSyncJob.perform_later(sync_type: "health_check")'
end
